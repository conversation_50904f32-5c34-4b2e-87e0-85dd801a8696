#!/usr/bin/env python3
"""Minimal Modal test with hardcoded credentials."""

import os

# Set credentials before importing modal
os.environ.update({
    "MODAL_TOKEN_ID": "ak-xbJoNYVYzlY4d7CQZXz76r",
    "MODAL_TOKEN_SECRET": "as-QfBo37x2DiENx6S7YvBJyL",
    "MODAL_CONFIG_PATH": "/tmp/nonexistent.toml"  # Avoid existing config
})

import modal

app = modal.App("minimal-test")

@app.function()
def hello():
    return "Hello from Modal!"

if __name__ == "__main__":
    print("Testing Modal with hardcoded credentials...")
    
    # Test local execution
    print("Local:", hello.local())
    
    # Test remote execution
    with app.run():
        print("Remote:", hello.remote())
    
    print("Success!")
